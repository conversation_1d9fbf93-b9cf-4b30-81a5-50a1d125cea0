using Microsoft.AspNetCore.Mvc;
using TPEOC.Services.Services.Fcp.Code;

namespace TPEOC.API.Controllers.Fcp;

/// <summary>FCP-代碼</summary>
[Route("api/fcp/code")]
[ApiController]
public class FcpCodeController: ControllerBase
{
    /// <summary>開設中專案(DPID)</summary>
    /// <param name="serv">服務</param>
    [HttpGet("opening-project")]
    public Task<List<FcpCodeOpeningProjectResult>> OpeningProject(
        [FromServices] IFcpCodeOpeningProjectService serv
        ) => serv.Invoke();
    
    /// <summary>台北市資源項目</summary>
    /// <param name="serv">服務</param>
    [HttpGet("equipment/type/main")]
    public Task<List<FcpCodeEquipmentTypeMainResult>> EquipmentTypeMain(
        [FromServices] IFcpCodeEquipmentTypeMainService serv
        ) => serv.Invoke();
    
    /// <summary>設備次類型</summary>
    /// <param name="serv">服務</param>
    /// <param name="query">查詢條件</param>
    [HttpGet("equipment/type/sub")]
    public Task<List<FcpCodeEquipmentTypeSubResult>> EquipmentTypeSub(
        [FromServices] IFcpCodeEquipmentTypeSubService serv,
        [FromQuery] FcpCodeEquipmentTypeSubQuery query
        ) => serv.Invoke(query);
    
    /// <summary>設備細類型</summary>
    /// <param name="serv">服務</param>
    /// <param name="query">查詢條件</param>
    [HttpGet("equipment/type/mini")]
    public Task<List<FcpCodeEquipmentTypeMiniResult>> EquipmentTypeMini(
        [FromServices] IFcpCodeEquipmentTypeMiniService serv,
        [FromQuery] FcpCodeEquipmentTypeMiniQuery query
        ) => serv.Invoke(query);
    
    /// <summary>台北市資源項目</summary>
    /// <param name="serv">服務</param>
    [HttpGet("equipment/main")]
    public Task<List<FcpCodeEquipmentResult>> EquipmentMain(
        [FromServices] IFcpCodeEquipmentMainService serv
        ) => serv.Invoke();
    
    /// <summary>現場資源項目</summary>
    /// <param name="serv">服務</param>
    [HttpGet("equipment/other")]
    public Task<List<FcpCodeEquipmentResult>> EquipmentOther(
        [FromServices] IFcpCodeEquipmentOtherService serv
        ) => serv.Invoke();

    /// <summary>職責小組</summary>
    /// <param name="serv">服務</param>
    /// <param name="query">查詢條件</param>
    [HttpGet("duty")]
    public Task<List<FcpCodeDutyResult>> Duty(
        [FromServices] IFcpCodeDutyService serv,
        [FromQuery] FcpCodeDutyQuery query
        ) => serv.Invoke(query);
    
    /// <summary>前指所手機號碼</summary>
    /// <param name="serv">服務</param>
    [HttpGet("mobile")]
    public Task<FcpCodeMobileResult> Mobile(
        [FromServices] IFcpCodeMobileService serv
        ) => serv.Invoke();
}
