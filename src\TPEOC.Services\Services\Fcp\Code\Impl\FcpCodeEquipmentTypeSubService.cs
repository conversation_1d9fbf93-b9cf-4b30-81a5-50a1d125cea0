using TPEOC.Repositories.TPEOC.Dpm;
using TPEOC.Utility.Aspects.CacheAspect;

namespace TPEOC.Services.Services.Fcp.Code.Impl;

public class FcpCodeEquipmentTypeSubService(
    IDpmEquipmentTypeRepository equipmentTypeRepo
    ): IFcpCodeEquipmentTypeSubService
{
    [Cache]
    public Task<List<FcpCodeEquipmentTypeSubResult>> Invoke(FcpCodeEquipmentTypeSubQuery query) => 
        equipmentTypeRepo.Query()
            .Where(x => x.MainType == query.MainType!.Value)
            .GroupBy(x => x.SubType)
            .Select(g => new FcpCodeEquipmentTypeSubResult
            {
                SubType = g.Key,
                SubTypeName = g.First().SubTypeName
            })
            .OrderBy(x => x.SubType)
            .ToListAsync();
}