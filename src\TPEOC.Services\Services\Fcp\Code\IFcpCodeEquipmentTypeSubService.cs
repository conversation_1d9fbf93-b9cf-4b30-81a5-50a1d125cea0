using System.ComponentModel.DataAnnotations;
using TPEOC.Services.Attributes.Validation;

namespace TPEOC.Services.Services.Fcp.Code;

public interface IFcpCodeEquipmentTypeSubService
{
    Task<List<FcpCodeEquipmentTypeSubResult>> Invoke(FcpCodeEquipmentTypeSubQuery query);
}

public class FcpCodeEquipmentTypeSubResult
{
    /// <summary>設備次類型代碼</summary>
    public int SubType { get; set; }

    /// <summary>設備次類型名稱</summary>
    public string SubTypeName { get; set; }
}

public class FcpCodeEquipmentTypeSubQuery
{
    /// <summary>設備主類型代碼</summary>
    [Display(Name = "設備主類型代碼"), RequiredZh]
    public int? MainType { get; set; }
}