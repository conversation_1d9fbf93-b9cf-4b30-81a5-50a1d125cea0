using System.Diagnostics.CodeAnalysis;
using TPEOC.Utility.Extensions;

namespace TPEOC.Repositories.TPEOC.Sys.Impl;

public class SysAccountRepository(
    TPEOCContext db,
    ISysDepartmentRepository deptRepo
    ) :
    Repository<SysAccount>(db), ISysAccountRepository
{
    private readonly TPEOCContext _db = db;
    protected override string DisplayName => "人員帳號";

    [SuppressMessage("Microsoft.EntityFrameworkCore", "EF1001")]
    private IQueryable<SysAccount> Query(
        bool includeRoles = false,
        bool includeDistrictEoc = false,
        bool includeEmAccount = false)
    {
        var query = base.Query();
        if (includeRoles) query = query.Include(o => o.SysAccountRoles);
        if (includeDistrictEoc) query = query.Include(o => o.SysAccountDistrictEOCs);
        if (includeEmAccount) query = query.Include(o => o.SysEMAccounts);
        return query;
    }

    public async Task<string?> GetUserName(Guid? userId) =>
        userId is null ? null : 
            await Query()
                .Where(o => o.UserID == userId.Value)
                .Select(o => o.Name)
                .FirstOrDefaultAsync();

    public async Task<SysAccount> Get(Guid userId, 
        bool includeRoles = false,
        bool includeDistrictEoc = false,
        bool includeEmAccount = false
        ) => 
        await Query(includeRoles, includeDistrictEoc, includeEmAccount)
            .FirstOrDefaultAsync(o => o.UserID == userId) ?? throw NotFound();

    public Task<Dictionary<Guid, string>> NameMap(IReadOnlyCollection<Guid> userIds) => 
        Query().Where(o => userIds.Contains(o.UserID)).ToDictionaryAsync(o => o.UserID, o => o.Name);

    public async Task Add(SysAccount user)
    {
        await _db.SysAccounts.AddAsync(user);
        await _db.SaveChangesAsync();
    }

    public Task<SysAccount?> GetByUsername(string username)
    {
        return Query().Where(o => o.UserName == username).FirstOrDefaultAsync();
    }

    public async Task<List<SysAccount>> GetByDepts(IEnumerable<SysDepartmentSlim> depts)
    {
        var sysDepts = depts.ToList();
        return (await Impl().ToListAsync()).Flatten()
            .GroupBy(o => o.UserID).Select(g => g.First())
            .ToList();
        
        async IAsyncEnumerable<List<SysAccount>> Impl()
        {
            var deptIdLevel1s = sysDepts.Where(o => o.DeptLevel == 1).Select(o => o.DeptID).ToHashSet();
            if (deptIdLevel1s.Count > 0)
                yield return await Query()
                    .Where(o => deptIdLevel1s.Contains(o.DeptIDLevel1!))
                    .ToListAsync();
        
            var deptIdLevel2s = sysDepts.Where(o => o.DeptLevel == 2).Select(o => o.DeptID).ToHashSet();
            if (deptIdLevel2s.Count > 0)
                yield return await Query()
                    .Where(o => deptIdLevel2s.Contains(o.DeptIDLevel2!))
                    .ToListAsync();
        }
    }

    public async Task<List<SysAccount>> GetByDeptIds(HashSet<string> deptIds)
    {
        var depts = (await deptRepo.List()).Where(o => deptIds.Contains(o.DeptID));
        return await GetByDepts(depts);
    }
}
