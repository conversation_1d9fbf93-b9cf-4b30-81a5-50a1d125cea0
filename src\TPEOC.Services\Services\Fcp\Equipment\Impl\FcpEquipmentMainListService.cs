using TPEOC.Repositories.TPEOC.Dpm;
using TPEOC.Repositories.TPEOC.Sys;
using TPEOC.Services.Enums;

namespace TPEOC.Services.Services.Fcp.Equipment.Impl;

public class FcpEquipmentMainListService(
    IDpmInventoryRecordRepository inventoryRepo,
    IDpmEquipmentRepository equipmentRepo,
    IDpmResourceAllocationRepository allocationRepo,
    ISysAccountRepository sysAccountRepo) : IFcpEquipmentMainListService
{
    public async Task<FcpEquipmentMainListResult> Invoke(Guid dpid, FcpEquipmentMainListQuery query)
    {
        var equipments = await FilterEquipments(query);
        var items = await BuildEquipmentData(dpid, equipments);
        var filteredItems = FilterByRequirement(items, query.EquipmentReq);
        var record = await GetLatestInventoryRecord(dpid, filteredItems.Select(x => x.EquipID).ToList());
        return new FcpEquipmentMainListResult
        {
            Items = filteredItems,
            CreatorName = record?.Creator is { } creatorId ? await sysAccountRepo.GetUserName(creatorId) : null,
            CreateTime = record?.CreateTime
        };
    }

    private async Task<List<DpmEquipmentCompound>> FilterEquipments(FcpEquipmentMainListQuery query) =>
        (await equipmentRepo.GetAll())
            .Where(e => e.MiniType == query.MiniType, query.MiniType is not null)
            .Where(e => e.EquipName.Contains(query.Keyword!) ||
                        e.MainTypeName.Contains(query.Keyword!) ||
                        e.SubTypeName.Contains(query.Keyword!) ||
                        e.MiniTypeName.Contains(query.Keyword!), 
                !string.IsNullOrWhiteSpace(query.Keyword))
            .ToList();

    private async Task<List<FcpEquipmentMainListResult_Item>> BuildEquipmentData(Guid dpid, List<DpmEquipmentCompound> equipments)
    {
        var equipmentIds = equipments.Select(e => e.EquipID).ToList();

        var inventoryRecords = await inventoryRepo.Query()
            .Where(r => r.DPID == dpid && !r.IsOtherEquipment && equipmentIds.Contains(r.EquipID))
            .ToListAsync();

        var allocations = await allocationRepo.Query().Where(a => equipmentIds.Contains(a.EquipID)).ToListAsync();

        var inventoryLookup = inventoryRecords.ToLookup(r => r.EquipID);
        var allocationLookup = allocations.ToLookup(a => a.EquipID);
        return equipments.Select(equipment => CreateEquipmentItem(equipment, inventoryLookup, allocationLookup)).ToList();
    }

    private static FcpEquipmentMainListResult_Item CreateEquipmentItem(
        DpmEquipmentCompound equipment,
        ILookup<Guid, DpmInventoryRecord> inventoryLookup,
        ILookup<Guid, DpmResourceAllocation> allocationLookup)
    {
        var inventoryRecords = inventoryLookup[equipment.EquipID].ToList();
        var allocations = allocationLookup[equipment.EquipID].ToList();

        return new FcpEquipmentMainListResult_Item
        {
            EquipID = equipment.EquipID,
            EquipName = equipment.EquipName,
            Quantity = equipment.Quantity,
            CheckInQuantity = inventoryRecords.Where(r => r.InventoryType == 1).Sum(r => r.Quantity),
            CheckOutQuantity = inventoryRecords.Where(r => r.InventoryType == 2).Sum(r => r.Quantity),
            ConsumedQuantity = inventoryRecords.Where(r => r.InventoryType == 3).Sum(r => r.Quantity),
            DemandQuantity = allocations.Sum(a => a.DemandQuantity),
            AssignQuantity = allocations.Sum(a => a.AssignQuantity)
        };
    }

    private static List<FcpEquipmentMainListResult_Item> FilterByRequirement(
        List<FcpEquipmentMainListResult_Item> items,
        EquipmentRequirement? requirement) =>
        requirement switch
        {
            EquipmentRequirement.不足 => items.Where(x => x.CurrentStock - x.PendingRequestQuantity < 2).ToList(),
            EquipmentRequirement.可能不足 => items.Where(x => x.CurrentStock - x.PendingRequestQuantity < 1).ToList(),
            _ => items
        };

    private async Task<DpmInventoryRecord?> GetLatestInventoryRecord(Guid dpid, IReadOnlyCollection<Guid> equipmentIds) =>
        await inventoryRepo.Query()
            .Where(r => r.DPID == dpid && !r.IsOtherEquipment && equipmentIds.Contains(r.EquipID))
            .OrderByDescending(r => r.CreateTime)
            .FirstOrDefaultAsync();
}