using TPEOC.Repositories.TPEOC.Dpm;
using TPEOC.Repositories.TPEOC.Sys;
using TPEOC.Services.Enums;

namespace TPEOC.Services.Services.Fcp.Equipment.Impl;

public class FcpEquipmentMainListService(
    IDpmInventoryRecordRepository inventoryRepo,
    IDpmEquipmentRepository equipmentRepo,
    IDpmResourceAllocationRepository allocationRepo,
    ISysAccountRepository sysAccountRepo) : IFcpEquipmentMainListService
{
    public async Task<FcpEquipmentMainListResult> Invoke(Guid dpid, FcpEquipmentMainListQuery query)
    {
        var equipments = await FilterEquipments(query);
        var equipmentIds = equipments.Select(e => e.EquipID).ToList();
        var inventoryRecords = await inventoryRepo.Query()
            .Where(r => r.DPID == dpid && !r.IsOtherEquipment && equipmentIds.Contains(r.EquipID))
            .ToListAsync();
        var allocations = await allocationRepo.Query()
            .Where(a => equipmentIds.Contains(a.EquipID))
            .ToListAsync();

        var latestRecord = inventoryRecords.OrderByDescending(r => r.CreateTime).FirstOrDefault();
        return new FcpEquipmentMainListResult
        {
            Items = BuildEquipmentData(equipments, inventoryRecords, allocations)
                .Apply(FilterByRequirement, query.EquipmentReq),
            CreatorName = latestRecord?.Creator is { } creatorId ? await sysAccountRepo.GetUserName(creatorId) : null,
            CreateTime = latestRecord?.CreateTime
        };
    }

    private async Task<List<DpmEquipmentCompound>> FilterEquipments(FcpEquipmentMainListQuery query) =>
        (await equipmentRepo.GetAll())
            .Where(e => e.MiniType == query.MiniType, query.MiniType is not null)
            .Where(e => e.EquipName.Contains(query.Keyword!) ||
                        e.MainTypeName.Contains(query.Keyword!) ||
                        e.SubTypeName.Contains(query.Keyword!) ||
                        e.MiniTypeName.Contains(query.Keyword!), 
                !string.IsNullOrWhiteSpace(query.Keyword))
            .ToList();

    private static List<FcpEquipmentMainListResult_Item> BuildEquipmentData(
        List<DpmEquipmentCompound> equipments,
        List<DpmInventoryRecord> inventoryRecords,
        List<DpmResourceAllocation> allocations)
    {
        var inventoryLookup = inventoryRecords.ToLookup(r => r.EquipID);
        var allocationLookup = allocations.ToLookup(a => a.EquipID);

        return equipments.Select(equipment =>
        {
            var equipInventoryRecords = inventoryLookup[equipment.EquipID].ToList();
            var equipAllocations = allocationLookup[equipment.EquipID].ToList();

            return new FcpEquipmentMainListResult_Item
            {
                EquipID = equipment.EquipID,
                EquipName = equipment.EquipName,
                Quantity = equipment.Quantity,
                CheckInQuantity = equipInventoryRecords.Where(r => r.InventoryType == 1).Sum(r => r.Quantity),
                CheckOutQuantity = equipInventoryRecords.Where(r => r.InventoryType == 2).Sum(r => r.Quantity),
                ConsumedQuantity = equipInventoryRecords.Where(r => r.InventoryType == 3).Sum(r => r.Quantity),
                AssignQuantity = equipAllocations.Sum(a => a.AssignQuantity),
                DemandQuantity = equipAllocations.Sum(a => a.DemandQuantity)
            };
        }).ToList();
    }

    private static List<FcpEquipmentMainListResult_Item> FilterByRequirement(
        List<FcpEquipmentMainListResult_Item> items,
        EquipmentRequirement? requirement) =>
        requirement switch
        {
            EquipmentRequirement.不足 => items.Where(x => x.CurrentStock - x.PendingRequestQuantity < 2).ToList(),
            EquipmentRequirement.可能不足 => items.Where(x => x.CurrentStock - x.PendingRequestQuantity < 1).ToList(),
            _ => items
        };
}