using TPEOC.Repositories.TPEOC.Dpm;
using TPEOC.Repositories.TPEOC.Sys;
using TPEOC.Services.Enums;
using TPEOC.Shared.Extensions;

namespace TPEOC.Services.Services.Fcp.Equipment.Impl;

public class FcpEquipmentMainListService(
    IDpmInventoryRecordRepository inventoryRepo,
    IDpmEquipmentRepository equipmentRepo,
    IDpmResourceAllocationRepository allocationRepo,
    ISysAccountRepository sysAccountRepo) : IFcpEquipmentMainListService
{
    public async Task<FcpEquipmentMainListResult> Invoke(Guid dpid, FcpEquipmentMainListQuery query) =>
        await query
            .ApplyAsync(FilterEquipments)
            .ApplyAsync(equipments => BuildEquipmentData(dpid, equipments))
            .ApplyAsync(data => data.Apply(FilterByRequirement, query.EquipmentReq))
            .ApplyAsync(async items => new FcpEquipmentMainListResult
            {
                Items = items,
                CreatorName = await GetCreatorName(dpid, items.Select(x => x.EquipID)),
                CreateTime = await GetCreateTime(dpid, items.Select(x => x.EquipID))
            });

    private async Task<List<DpmEquipmentCompound>> FilterEquipments(FcpEquipmentMainListQuery query) =>
        (await equipmentRepo.GetAll())
            .Where(e => e.MiniType == query.MiniType, query.MiniType is not null)
            .Where(e => ContainsKeyword(e, query.Keyword!), !string.IsNullOrWhiteSpace(query.Keyword))
            .ToList();

    private async Task<List<FcpEquipmentMainListResult_Item>> BuildEquipmentData(Guid dpid, List<DpmEquipmentCompound> equipments)
    {
        var equipmentIds = equipments.Select(e => e.EquipID).ToList();

        var inventoryRecords = await inventoryRepo.Query()
            .Where(r => r.DPID == dpid && !r.IsOtherEquipment && equipmentIds.Contains(r.EquipID))
            .ToListAsync();

        var allocations = await allocationRepo.Query()
            .Where(a => equipmentIds.Contains(a.EquipID))
            .ToListAsync();

        return equipments
            .Apply(CreateLookups, inventoryRecords, allocations)
            .Apply(BuildItems);
    }

    private static bool ContainsKeyword(DpmEquipmentCompound equipment, string keyword) =>
        equipment.EquipName.Contains(keyword) ||
        equipment.MainTypeName.Contains(keyword) ||
        equipment.SubTypeName.Contains(keyword) ||
        equipment.MiniTypeName.Contains(keyword);

    private static (List<DpmEquipmentCompound> equipments, ILookup<Guid, DpmInventoryRecord> inventoryLookup, ILookup<Guid, DpmResourceAllocation> allocationLookup) CreateLookups(
        List<DpmEquipmentCompound> equipments,
        List<DpmInventoryRecord> inventoryRecords,
        List<DpmResourceAllocation> allocations) =>
        (equipments, inventoryRecords.ToLookup(r => r.EquipID), allocations.ToLookup(a => a.EquipID));

    private static List<FcpEquipmentMainListResult_Item> BuildItems(
        (List<DpmEquipmentCompound> equipments, ILookup<Guid, DpmInventoryRecord> inventoryLookup, ILookup<Guid, DpmResourceAllocation> allocationLookup) data) =>
        data.equipments.Select(equipment => CreateEquipmentItem(equipment, data.inventoryLookup, data.allocationLookup)).ToList();

    private static FcpEquipmentMainListResult_Item CreateEquipmentItem(
        DpmEquipmentCompound equipment,
        ILookup<Guid, DpmInventoryRecord> inventoryLookup,
        ILookup<Guid, DpmResourceAllocation> allocationLookup)
    {
        var inventoryRecords = inventoryLookup[equipment.EquipID].ToList();
        var allocations = allocationLookup[equipment.EquipID].ToList();

        return new FcpEquipmentMainListResult_Item
        {
            EquipID = equipment.EquipID,
            EquipName = equipment.EquipName,
            Quantity = equipment.Quantity,
            CheckInQuantity = inventoryRecords.Where(r => r.InventoryType == 1).Sum(r => r.Quantity),
            CheckOutQuantity = inventoryRecords.Where(r => r.InventoryType == 2).Sum(r => r.Quantity),
            ConsumedQuantity = inventoryRecords.Where(r => r.InventoryType == 3).Sum(r => r.Quantity),
            DemandQuantity = allocations.Sum(a => a.DemandQuantity),
            AssignQuantity = allocations.Sum(a => a.AssignQuantity)
        };
    }

    private static List<FcpEquipmentMainListResult_Item> FilterByRequirement(
        List<FcpEquipmentMainListResult_Item> items,
        EquipmentRequirement? requirement) =>
        requirement switch
        {
            EquipmentRequirement.不足 => items.Where(x => (x.CurrentStock - x.PendingRequestQuantity) < 0).ToList(),
            EquipmentRequirement.可能不足 => items.Where(x => (x.CurrentStock - x.PendingRequestQuantity) <= 0).ToList(),
            _ => items
        };

    private async Task<string?> GetCreatorName(Guid dpid, IEnumerable<Guid> equipmentIds) =>
        await GetLatestInventoryRecord(dpid, equipmentIds)
            .ApplyAsync(async record => record?.Creator)
            .ApplyAsync(async creatorId => creatorId.HasValue ? await sysAccountRepo.GetUserName(creatorId.Value) : null);

    private async Task<DateTime?> GetCreateTime(Guid dpid, IEnumerable<Guid> equipmentIds) =>
        (await GetLatestInventoryRecord(dpid, equipmentIds))?.CreateTime;

    private async Task<DpmInventoryRecord?> GetLatestInventoryRecord(Guid dpid, IEnumerable<Guid> equipmentIds) =>
        await inventoryRepo.Query()
            .Where(r => r.DPID == dpid && !r.IsOtherEquipment && equipmentIds.Contains(r.EquipID))
            .OrderByDescending(r => r.CreateTime)
            .FirstOrDefaultAsync();
}