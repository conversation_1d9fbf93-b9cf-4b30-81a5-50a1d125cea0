using TPEOC.Repositories.TPEOC.Dpm;
using TPEOC.Repositories.TPEOC.Sys;
using TPEOC.Services.Enums;

namespace TPEOC.Services.Services.Fcp.Equipment.Impl;

public class FcpEquipmentMainListService(
    IDpmInventoryRecordRepository inventoryRepo,
    IDpmEquipmentRepository equipmentRepo,
    IDpmResourceAllocationRepository allocationRepo,
    ISysAccountRepository sysAccountRepo) : IFcpEquipmentMainListService
{
    public async Task<FcpEquipmentMainListResult> Invoke(Guid dpid, FcpEquipmentMainListQuery query)
    {
        var equipments = await GetEquipmentsWithFiltering(query);
        var equipmentIds = equipments.Select(e => e.EquipID).ToList();

        var inventoryRecords = await inventoryRepo.Query()
            .Where(r => r.DPID == dpid && !r.IsOtherEquipment && equipmentIds.Contains(r.EquipID))
            .ToListAsync();

        var allocations = await allocationRepo.Query()
            .Where(a => equipmentIds.Contains(a.EquipID))
            .ToListAsync();

        var equipmentData = BuildEquipmentDataWithLookups(equipments, inventoryRecords, allocations);
        var filteredData = ApplyRequirementFilter(equipmentData, query.EquipmentReq);
        var (creatorName, createTime) = await GetLatestCreatorInfo(inventoryRecords);

        return new FcpEquipmentMainListResult
        {
            Items = filteredData,
            CreatorName = creatorName,
            CreateTime = createTime
        };
    }

    private async Task<List<DpmEquipmentCompound>> GetEquipmentsWithFiltering(FcpEquipmentMainListQuery query)
    {
        var allEquipments = await equipmentRepo.GetAll();

        return allEquipments
            .Where(e => query.MiniType == null || e.MiniType == query.MiniType)
            .Where(e => string.IsNullOrWhiteSpace(query.Keyword) ||
                       e.EquipName.Contains(query.Keyword) ||
                       e.MainTypeName.Contains(query.Keyword) ||
                       e.SubTypeName.Contains(query.Keyword) ||
                       e.MiniTypeName.Contains(query.Keyword))
            .ToList();
    }

    private static List<FcpEquipmentMainListResult_Item> BuildEquipmentDataWithLookups(
        List<DpmEquipmentCompound> equipments,
        List<DpmInventoryRecord> inventoryRecords,
        List<DpmResourceAllocation> allocations)
    {
        var inventoryLookup = inventoryRecords.ToLookup(r => r.EquipID);
        var allocationLookup = allocations.ToLookup(a => a.EquipID);

        return equipments.Select(equipment =>
        {
            var equipInventoryRecords = inventoryLookup[equipment.EquipID].ToList();
            var equipAllocations = allocationLookup[equipment.EquipID].ToList();

            return new FcpEquipmentMainListResult_Item
            {
                EquipID = equipment.EquipID,
                EquipName = equipment.EquipName,
                Quantity = equipment.Quantity,
                CheckInQuantity = equipInventoryRecords.Where(r => r.InventoryType == 1).Sum(r => r.Quantity),
                CheckOutQuantity = equipInventoryRecords.Where(r => r.InventoryType == 2).Sum(r => r.Quantity),
                ConsumedQuantity = equipInventoryRecords.Where(r => r.InventoryType == 3).Sum(r => r.Quantity),
                DemandQuantity = equipAllocations.Sum(a => a.DemandQuantity),
                AssignQuantity = equipAllocations.Sum(a => a.AssignQuantity)
            };
        }).ToList();
    }

    private static List<FcpEquipmentMainListResult_Item> ApplyRequirementFilter(
        List<FcpEquipmentMainListResult_Item> equipmentData,
        EquipmentRequirement? equipmentReq) =>
        equipmentReq switch
        {
            EquipmentRequirement.不足 => equipmentData.Where(x => (x.CurrentStock - x.PendingRequestQuantity) < 0).ToList(),
            EquipmentRequirement.可能不足 => equipmentData.Where(x => (x.CurrentStock - x.PendingRequestQuantity) <= 0).ToList(),
            _ => equipmentData
        };

    private async Task<(string? creatorName, DateTime? createTime)> GetLatestCreatorInfo(List<DpmInventoryRecord> inventoryRecords)
    {
        if (inventoryRecords.Count == 0)
            return (null, null);

        var latestRecord = inventoryRecords.OrderByDescending(r => r.CreateTime).First();
        var creatorName = await sysAccountRepo.GetUserName(latestRecord.Creator);

        return (creatorName, latestRecord.CreateTime);
    }
}