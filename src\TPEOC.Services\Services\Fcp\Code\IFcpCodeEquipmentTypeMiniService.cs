using System.ComponentModel.DataAnnotations;
using TPEOC.Services.Attributes.Validation;

namespace TPEOC.Services.Services.Fcp.Code;

public interface IFcpCodeEquipmentTypeMiniService
{
    Task<List<FcpCodeEquipmentTypeMiniResult>> Invoke(FcpCodeEquipmentTypeMiniQuery query);
}

public class FcpCodeEquipmentTypeMiniResult
{
    /// <summary>設備細類型代碼</summary>
    public int MiniType { get; set; }

    /// <summary>設備細類型名稱</summary>
    public string MiniTypeName { get; set; }
}

public class FcpCodeEquipmentTypeMiniQuery
{
    /// <summary>設備主類型代碼</summary>
    [Display(Name = "設備主類型代碼"), RequiredZh]
    public int? MainType { get; set; }

    /// <summary>設備次類型代碼</summary>
    [Display(Name = "設備次類型代碼"), RequiredZh]
    public int? SubType { get; set; }
}