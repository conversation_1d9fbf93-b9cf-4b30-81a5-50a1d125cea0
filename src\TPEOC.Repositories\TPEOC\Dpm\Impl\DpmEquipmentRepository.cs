namespace TPEOC.Repositories.TPEOC.Dpm.Impl;

public class DpmEquipmentRepository(
    TPEOCContext db,
    IEntityCache<DpmEquipment> entityCache,
    IDpmEquipmentTypeRepository equipmentTypeRepo
    ) : 
    Repository<DpmEquipment>(db), IDpmEquipmentRepository
{
    protected override string DisplayName => "主要設備";
    
    public Task<List<DpmEquipmentCompound>> GetAll() => entityCache.Get(() => 
        (from equipment in Query()
         join equipType in equipmentTypeRepo.Query()
             on equipment.MiniType equals equipType.MiniType
         select new DpmEquipmentCompound 
         { 
             EquipID = equipment.EquipID, 
             EquipName = equipment.EquipName,
             MiniType = equipment.MiniType,
             Quantity = equipment.Quantity,
             MainTypeName = equipType.MainTypeName,
             SubTypeName = equipType.SubTypeName,
             MiniTypeName = equipType.MiniTypeName
         }).ToListAsync()
    );
}
