# Issue #71 - 人員管理現場資源功能

## 需求概述

提供人員管理現場資源入庫、出庫、報銷功能

**相關文件**: [8/13 DB文件](https://iisicloud.sharepoint.com/:w:/r/sites/msteams_7b1d0c/Shared%20Documents/D250521-114%E5%B9%B4%E5%BA%A6/%E7%B3%BB%E7%B5%B1%E5%88%86%E6%9E%90/%E8%B3%87%E6%96%99%E5%BA%AB/TableSchema(20250813).docx?d=w46e9194dbc3a481fac0de13908c3762a&csf=1&web=1&e=61Q34F)

---

## 一、臺北市資源項目管理

### 參考畫面
![臺北市資源項目管理畫面](/uploads/80092aeabb8aa53880afc639db3b7716/image.png)

### 功能說明

#### 查詢條件

1. **搜尋條件下拉式選單**
   - 資源主項、資源次項、資源細項（來源：`DpmEquipmentType`）
   - 資源設備（來源：`DpmEquipment`）

2. **資源需求篩選**（Radio Button）
   - **全部**（預設）：顯示所有資源
   - **不足**：現場庫存 - 尚請求數量 < 2
   - **可能不足**：現場庫存 - 尚請求數量 < 1

#### 匯出功能

由前端實作查詢結果匯出 Excel 功能

#### 查詢結果

**條件**: `DpmInventoryRecord.IsOtherEquipment = 0`，依 `EquipID` 條列

| 欄位名稱 | 資料來源 |
|---------|----------|
| 資源項目 | `DpmInventoryRecord.EquipID` |
| EMIC數量 | `DpmEquipment.Quantity` |
| 入庫(登記)數量 | `DpmInventoryRecord.InventoryType = 1` 加總 |
| 出庫(登記)數量 | `DpmInventoryRecord.InventoryType = 2` 加總 |
| 消耗數量 | `DpmInventoryRecord.InventoryType = 3` 加總 |
| 現場庫存 | 入庫數量 - 出庫數量 - 消耗數量 |
| 已分派數量 | 依 `DpmResourceAllocation.EquipID` 加總 `AssignQuantity` |
| 剩餘數量 | 現場庫存 - 已分派數量 |
| 需求數量 | 依 `DpmResourceAllocation.EquipID` 加總 `DpmResourceAllocation.DemandQuantity` |
| 指派數量 | 依 `DpmResourceAllocation.EquipID` 加總 `DpmResourceAllocation.AssignQuantity` |
| 尚請求數量 | 需求數量 - 指派數量 即 (依 `DpmResourceAllocation.EquipID` 加總 `DemandQuantity`) - (依 `DpmResourceAllocation.EquipID` 加總 `AssignQuantity`) |

#### 詳情檢視

![詳情檢視畫面](/uploads/f3d2169f8d398ac20dc29c6979a851b8/image.png)

**注意**: 畫面需新增「備註」欄位

**資料來源**: 依 `DpmInventoryRecord.EquipID` 取得記錄清單，包含：

- **時間**: `CreateTime`
- **動作**: `InventoryType`
- **資源項目**: `DpmEquipment.EquipName`
- **數量**: `Quantity`
- **備註**: `Remark`
- **局處單位**: 依 `Creator` 取 `SysAccount.DeptIDLevel1`
- **人員**: `Creator`

#### 產製資訊

**位置**: 右上角

**資料來源**: `DpmInventoryRecord.IsOtherEquipment = 0` 的最新一筆記錄
- **產製人**: `Creator`
- **產製日期**: `CreateTime`

---

## 二、現場自建資源項目管理

### 參考畫面
![現場自建資源項目管理畫面](/uploads/e9b676fa7f95ce6a965001222c73f238/image.png)

### 功能說明

#### 查詢條件

1. **搜尋條件下拉式選單**
   - 資源設備（來源：`DpmOtherEquipment`）

2. **資源需求篩選**（Radio Button）
   - **全部**（預設）：顯示所有資源
   - **不足**：現場庫存 - 尚請求數量 < 2
   - **可能不足**：現場庫存 - 尚請求數量 < 1

#### 匯出功能

由前端實作查詢結果匯出 Excel 功能

#### 查詢結果

**條件**: `DpmInventoryRecord.IsOtherEquipment = 1`，依 `EquipID` 條列

| 欄位名稱 | 資料來源 |
|---------|----------|
| 資源項目 | `DpmInventoryRecord.EquipID` |
| ~~EMIC數量~~ | **移除此欄位** |
| 入庫(登記)數量 | `DpmInventoryRecord.InventoryType = 1` 加總 |
| 出庫(登記)數量 | `DpmInventoryRecord.InventoryType = 2` 加總 |
| 消耗數量 | `DpmInventoryRecord.InventoryType = 3` 加總 |
| 現場庫存 | 入庫數量 - 出庫數量 - 消耗數量 |
| 已分派數量 | 依 `DpmResourceAllocation.EquipID` 加總 `AssignQuantity` |
| 剩餘數量 | 現場庫存 - 已分派數量 |
| 需求數量 | 依 `DpmResourceAllocation.EquipID` 加總 `DpmResourceAllocation.DemandQuantity` |
| 指派數量 | 依 `DpmResourceAllocation.EquipID` 加總 `DpmResourceAllocation.AssignQuantity` |
| 尚請求數量 | 需求數量 - 指派數量 即 (依 `DpmResourceAllocation.EquipID` 加總 `DemandQuantity`) - (依 `DpmResourceAllocation.EquipID` 加總 `AssignQuantity`) |

#### 詳情檢視

![詳情檢視畫面](/uploads/6163683a47249ebb3dfa9a61778d8b9c/image.png)

**注意**: 畫面需新增「備註」欄位（將補充介面）

**資料來源**: 依 `DpmInventoryRecord.EquipID` 取得記錄清單，包含：

- **時間**: `CreateTime`
- **動作**: `InventoryType`
- **資源項目**: `DpmOtherEquipment.EquipName`
- **數量**: `Quantity`
- **備註**: `Remark`
- **局處單位**: 依 `Creator` 取 `SysAccount.DeptIDLevel1`
- **人員**: `Creator`

#### 產製資訊

**位置**: 右上角

**資料來源**: `DpmInventoryRecord.IsOtherEquipment = 1` 的最新一筆記錄
- **產製人**: `Creator`
- **產製日期**: `CreateTime`

---

## 資料庫欄位說明

### InventoryType 定義

| 值 | 說明 |
|----|------|
| 1 | 入庫(登記) |
| 2 | 出庫(登記) |
| 3 | 消耗 |

### IsOtherEquipment 定義

| 值 | 說明 |
|----|------|
| 0 | 臺北市資源項目 |
| 1 | 現場自建資源項目 |