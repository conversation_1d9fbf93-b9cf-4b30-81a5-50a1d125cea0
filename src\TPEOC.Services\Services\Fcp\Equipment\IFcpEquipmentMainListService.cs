using System.ComponentModel.DataAnnotations;
using TPEOC.Services.Attributes.Validation;
using TPEOC.Services.Enums;

namespace TPEOC.Services.Services.Fcp.Equipment;

public interface IFcpEquipmentMainListService
{
    Task<FcpEquipmentMainListResult> Invoke(Guid dpid, FcpEquipmentMainListQuery query);
}

public class FcpEquipmentMainListQuery
{
    /// <summary>設備細類型代碼</summary>
    public int? MiniType { get; set; }
    
    /// <summary>關鍵字(各類型名稱、資源項目)</summary>
    public string? Keyword { get; set; }
    
    /// <summary>資源需求(全部、不足、可能不足)</summary>
    [Display(Name = "資源需求"), RequiredZh]
    public EquipmentRequirement? EquipmentReq { get; set; }
}

public class FcpEquipmentMainListResult
{
    /// <summary>產製人姓名</summary>
    public string? UpdaterName { get; set; }
    
    /// <summary>產製人日期</summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>資料清單</summary>
    public List<FcpEquipmentMainListResult_Item> Items { get; set; } = [];
}

public class FcpEquipmentMainListResult_Item
{
    /// <summary>設備編號</summary>
    public Guid EquipID { get; set; }
    
    /// <summary>資源項目（設備名稱）- 來源：DpmInventoryRecord.EquipID 對應的設備名稱</summary>
    public string EquipName { get; set; }
    
    /// <summary>EMIC數量</summary>
    public int Quantity { get; set; }
    
    /// <summary>入庫(登記)數量 - InventoryType = 1 的加總</summary>
    public int CheckInQuantity { get; set; }
    
    /// <summary>出庫(登記)數量 - InventoryType = 2 的加總</summary>
    public int CheckOutQuantity { get; set; }
    
    /// <summary>消耗數量 - InventoryType = 3 的加總</summary>
    public int ConsumedQuantity { get; set; }
    
    /// <summary>現場庫存：入庫數量扣除出庫數量及消耗數量後的實際可用數量</summary>
    public int CurrentStock => CheckInQuantity - CheckOutQuantity - ConsumedQuantity;
    
    /// <summary>已分派數量 - 依EquipID加總DpmResourceAllocation.AssignQuantity</summary>
    public int AssignQuantity { get; set; }
    
    /// <summary>剩餘數量：現場庫存扣除已分派數量後的可分派數量</summary>
    public int RemainingQuantity => CurrentStock - AssignQuantity;
    
    /// <summary>需求數量 - 依EquipID加總DpmResourceAllocation.DemandQuantity</summary>
    public int DemandQuantity { get; set; }
    
    /// <summary>指派數量 - 依EquipID加總DpmResourceAllocation.AssignQuantity（與已分派數量相同）</summary>
    public int AllocatedQuantity => AssignQuantity;
    
    /// <summary>尚請求數量：需求數量與指派數量的差額，表示仍需要請求的數量</summary>
    public int PendingRequestQuantity => DemandQuantity - AllocatedQuantity;
}
