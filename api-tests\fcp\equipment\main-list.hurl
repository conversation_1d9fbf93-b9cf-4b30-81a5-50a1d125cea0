# 測試 EquipmentReq 有效值
GET {{host}}/api/fcp/equipment/D3FA9C58-4B38-4FCA-B66B-003A464C565F/main/list
Authorization: Bearer {{token}}
[Query]
EquipmentReq: 0
HTTP 200

GET {{host}}/api/fcp/equipment/D3FA9C58-4B38-4FCA-B66B-003A464C565F/main/list
Authorization: Bearer {{token}}
[Query]
EquipmentReq: 1
HTTP 200

GET {{host}}/api/fcp/equipment/D3FA9C58-4B38-4FCA-B66B-003A464C565F/main/list
Authorization: Bearer {{token}}
[Query]
EquipmentReq: 2
HTTP 200

# 測試必填驗證 - 空值
GET {{host}}/api/fcp/equipment/D3FA9C58-4B38-4FCA-B66B-003A464C565F/main/list
Authorization: Bearer {{token}}
HTTP 400

# 測試無效值
GET {{host}}/api/fcp/equipment/D3FA9C58-4B38-4FCA-B66B-003A464C565F/main/list
Authorization: Bearer {{token}}
[Query]
EquipmentReq: 3
HTTP 400

GET {{host}}/api/fcp/equipment/D3FA9C58-4B38-4FCA-B66B-003A464C565F/main/list
Authorization: Bearer {{token}}
[Query]
EquipmentReq: -1
HTTP 400

GET {{host}}/api/fcp/equipment/D3FA9C58-4B38-4FCA-B66B-003A464C565F/main/list
Authorization: Bearer {{token}}
[Query]
EquipmentReq: invalid
HTTP 400

# 測試組合參數
GET {{host}}/api/fcp/equipment/D3FA9C58-4B38-4FCA-B66B-003A464C565F/main/list
Authorization: Bearer {{token}}
[Query]
MiniType: 101
Keyword: 消防車
EquipmentReq: 1
HTTP 200