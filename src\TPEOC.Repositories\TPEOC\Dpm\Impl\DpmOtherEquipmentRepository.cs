namespace TPEOC.Repositories.TPEOC.Dpm.Impl;

public class DpmOtherEquipmentRepository(
    TPEOCContext db,
    IEntityCache<DpmEquipment> entityCache
    ): Repository<DpmOtherEquipment>(db), IDpmOtherEquipmentRepository
{
    protected override string DisplayName => "其他設備(自建設備)";
    
    public Task<List<DpmEquipmentCompound>> GetAll() => entityCache.Get(() => 
        Query()
            .Select(o => new DpmEquipmentCompound { EquipID = o.EquipID, EquipName = o.EquipName })
            .ToListAsync()
    );
}