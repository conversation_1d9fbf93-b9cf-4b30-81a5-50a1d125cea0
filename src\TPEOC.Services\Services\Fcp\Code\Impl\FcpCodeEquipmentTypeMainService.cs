using TPEOC.Repositories.TPEOC.Dpm;
using TPEOC.Utility.Aspects.CacheAspect;

namespace TPEOC.Services.Services.Fcp.Code.Impl;

public class FcpCodeEquipmentTypeMainService(
    IDpmEquipmentTypeRepository equipmentTypeRepo
    ): IFcpCodeEquipmentTypeMainService
{
    [Cache]
    public Task<List<FcpCodeEquipmentTypeMainResult>> Invoke() => 
        equipmentTypeRepo.Query()
            .GroupBy(x => x.MainType)
            .Select(g => new FcpCodeEquipmentTypeMainResult
            {
                MainType = g.Key,
                MainTypeName = g.First().MainTypeName
            })
            .OrderBy(x => x.MainType)
            .ToListAsync();
}