using System.Reflection;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.ModelBinding.Binders;
using Microsoft.Extensions.Logging;

namespace TPEOC.Shared.Extensions.ServiceConfigurations;

public class EnumModelBinder(IModelBinder fallbackBinder) : IModelBinder
{
    private readonly IModelBinder _fallbackBinder = fallbackBinder ?? throw new ArgumentNullException(nameof(fallbackBinder));

    public Task BindModelAsync(ModelBindingContext bindingContext)
    {
        ArgumentNullException.ThrowIfNull(bindingContext);

        var modelName = bindingContext.ModelName;
        var valueProviderResult = bindingContext.ValueProvider.GetValue(modelName);

        if (valueProviderResult == ValueProviderResult.None)
        {
            return _fallbackBinder.BindModelAsync(bindingContext);
        }

        bindingContext.ModelState.SetModelValue(modelName, valueProviderResult);

        var value = valueProviderResult.FirstValue;

        if (string.IsNullOrEmpty(value))
        {
            return _fallbackBinder.BindModelAsync(bindingContext);
        }

        var actualType = Nullable.GetUnderlyingType(bindingContext.ModelType) ?? bindingContext.ModelType;

        try
        {
            var jsonStringEnumConverter = actualType
                .GetCustomAttribute<JsonConverterAttribute>()?.ConverterType == typeof(JsonStringEnumConverter);
            if (jsonStringEnumConverter)
            {
                if (Enum.TryParse(actualType, value, true, out var result))
                {
                    bindingContext.Result = ModelBindingResult.Success(result);
                    return Task.CompletedTask;
                }

                var validValues = Enum.GetNames(actualType);
                bindingContext.ModelState.TryAddModelError(modelName, GetErrorMessage(actualType, value, validValues));
                return Task.CompletedTask;
            }
            else
            {
                if (int.TryParse(value, out var intResult) && Enum.IsDefined(actualType, intResult))
                {
                    bindingContext.Result = ModelBindingResult.Success(Enum.ToObject(actualType, intResult));
                    return Task.CompletedTask;
                }

                var validValues = Enum.GetValues(actualType).Cast<int>();
                bindingContext.ModelState.TryAddModelError(modelName, GetErrorMessage(actualType, value, validValues));
                return Task.CompletedTask;
            }
        }
        catch (Exception ex)
        {
            bindingContext.ModelState.TryAddModelError(modelName, $"Could not convert value '{value}' to enum '{actualType.Name}'. Exception: {ex.Message}");
            return Task.CompletedTask;
        }
    }
    
    private static string GetErrorMessage<T>(Type enumType, string value, IEnumerable<T> validValues)
    {
        if (typeof(T) == typeof(string))
        {
            // 對於字串 enum 名稱，顯示名稱加數值
            var options = validValues.Cast<string>().Select(name =>
            {
                if (Enum.TryParse(enumType, name, true, out var enumValue))
                {
                    return $"{name}({Convert.ToInt32(enumValue)})";
                }
                return name;
            });
            return $"無效值：'{value}'。請選擇：{string.Join("、", options)}";
        }
        else
        {
            // 對於數字值，需要取得對應的 enum 名稱
            var options = validValues.Cast<int>().Select(intValue =>
            {
                var enumValue = Enum.ToObject(enumType, intValue);
                return $"{enumValue}({intValue})";
            });
            return $"無效值：'{value}'。請選擇：{string.Join("、", options)}";
        }
    }
}


public class EnumModelBinderProvider(ILoggerFactory loggerFactory) : IModelBinderProvider
{
    private readonly ILoggerFactory _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));

    public IModelBinder? GetBinder(ModelBinderProviderContext context)
    {
        ArgumentNullException.ThrowIfNull(context);

        if (!context.Metadata.IsEnum && !(Nullable.GetUnderlyingType(context.Metadata.ModelType)?.IsEnum ?? false)) return null;
        var simpleBinder = new SimpleTypeModelBinder(context.Metadata.ModelType, _loggerFactory);
        return new EnumModelBinder(simpleBinder);
    }
}
