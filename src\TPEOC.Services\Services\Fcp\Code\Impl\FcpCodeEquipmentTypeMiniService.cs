using TPEOC.Repositories.TPEOC.Dpm;
using TPEOC.Utility.Aspects.CacheAspect;

namespace TPEOC.Services.Services.Fcp.Code.Impl;

public class FcpCodeEquipmentTypeMiniService(
    IDpmEquipmentTypeRepository equipmentTypeRepo
    ): IFcpCodeEquipmentTypeMiniService
{
    [Cache]
    public Task<List<FcpCodeEquipmentTypeMiniResult>> Invoke(FcpCodeEquipmentTypeMiniQuery query) => 
        equipmentTypeRepo.Query()
            .Where(x => x.MainType == query.MainType!.Value && x.SubType == query.SubType!.Value)
            .GroupBy(x => x.MiniType)
            .Select(g => new FcpCodeEquipmentTypeMiniResult
            {
                MiniType = g.Key,
                MiniTypeName = g.First().MiniTypeName
            })
            .OrderBy(x => x.MiniType)
            .ToListAsync();
}