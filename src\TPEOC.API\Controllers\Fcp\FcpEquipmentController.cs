using TPEOC.Services.Services.Fcp.Equipment;

namespace TPEOC.API.Controllers.Fcp;

/// <summary>FCP-資源記錄</summary>
[Route("api/fcp/equipment/{dpid:guid}")]
[ApiController]
public class FcpEquipmentController: ControllerBase
{
    /// <summary>臺北市資源項目</summary>
    /// <param name="serv">服務</param>
    /// <param name="dpid">應變專案編號</param>
    /// <param name="query">查詢條件</param>
    [HttpGet("main/list")]
    public Task<FcpEquipmentMainListResult> MainList(
        [FromServices] IFcpEquipmentMainListService serv,
        Guid dpid,
        [FromQuery] FcpEquipmentMainListQuery query
        ) => serv.Invoke(dpid, query);
}
