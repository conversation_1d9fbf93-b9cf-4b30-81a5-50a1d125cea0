namespace TPEOC.Repositories.TPEOC.Dpm;

public interface IDpmEquipmentRepository
{
    Task<List<DpmEquipmentCompound>> GetAll();
}

public class DpmEquipmentCompound
{
    public Guid EquipID { get; set; }
    public string EquipName { get; set; }
    public int MiniType { get; set; }
    public int Quantity { get; set; }
    public string MainTypeName { get; set; }
    public string SubTypeName { get; set; }
    public string MiniTypeName { get; set; }
}
